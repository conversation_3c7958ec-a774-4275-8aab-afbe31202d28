<template>
  <div class="love-img-container">
    <h4 class="page-title">记录下你的最美瞬间</h4>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    
    <!-- 图片网格 -->
    <div v-else-if="loveImages.length > 0" class="image-grid">
      <el-card 
        v-for="item in loveImages" 
        :key="item.id"
        class="love-card"
        :body-style="{ padding: '0' }"
        shadow="hover"
      >
        <div class="love-img-content">
          <img 
            :src="item.imgUrl" 
            :alt="item.imgText"
            class="love-image"
          />
          
          <div class="image-info">
            <i class="image-date">Date：{{ item.imgDate }}</i>
            <span class="image-text">{{ item.imgText }}</span>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无图片" />
    </div>
    
    <!-- 分页 -->
    <div v-if="totalImages > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalImages"
        layout="prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getLoveImages } from '../api/index.js'

const loading = ref(true)
const loveImages = ref([])
const totalImages = ref(0)
const currentPage = ref(1)
const pageSize = ref(12) // 每页显示12张图片

// 模拟数据，作为后备数据
const mockData = [
  {
    id: 1,
    imgUrl: '/images/sample1.jpg',
    imgText: '下班经过的路上，正好夕阳斜照着了',
    imgDate: '2024-11-07'
  },
  {
    id: 2,
    imgUrl: '/images/sample2.jpg', 
    imgText: '今天钓到了一条大鱼',
    imgDate: '2024-11-07'
  },
  {
    id: 3,
    imgUrl: '/images/sample3.jpg',
    imgText: '和朋友一起去爬山',
    imgDate: '2024-11-06'
  },
  {
    id: 4,
    imgUrl: '/images/sample4.jpg',
    imgText: '在咖啡厅度过的悠闲下午',
    imgDate: '2024-11-06'
  }
]

// 获取恋爱相册数据
const fetchLoveImages = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }
    
    const response = await getLoveImages(params)
    
    if (response && response.code === 200 && response.data) {
      loveImages.value = response.data.list || []
      totalImages.value = response.data.total || 0
    } else {
      // API 失败，使用 Mock 数据
      console.warn('API 响应异常，使用 Mock 数据')
      loveImages.value = mockData
      totalImages.value = mockData.length
    }
  } catch (error) {
    // API 请求失败，使用 Mock 数据
    console.warn('API 请求失败，使用 Mock 数据:', error)
    loveImages.value = mockData
    totalImages.value = mockData.length
  } finally {
    loading.value = false
  }
}

// 页面变化处理
const handlePageChange = (newPage) => {
  currentPage.value = newPage
  fetchLoveImages()
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

onMounted(() => {
  fetchLoveImages()
})
</script>

<style scoped>
.love-img-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  text-align: center;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
  color: #333;
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  justify-items: center;
}

.love-card {
  width: 100%;
  max-width: 350px;
  border-radius: 1.5rem;
  border: 1px solid rgba(208, 206, 206, 0.4);
  transition: all 0.3s ease;
  background: #fafafa;
}

.love-card:hover {
  background: #494949;
  cursor: pointer;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.love-img-content {
  padding: 1.3rem 1.3rem 1.5rem;
}

.love-image {
  width: 100%;
  height: 25rem;
  border-radius: 1rem;
  object-fit: cover;
  box-shadow: 0 0px 30px rgba(133, 125, 125, 0.47);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.love-card:hover .love-image {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(59, 59, 59, 0.68);
}

.image-info {
  padding: 0 1rem;
}

.image-date {
  display: block;
  text-align: right;
  font-style: normal;
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  color: #999;
  padding-bottom: 0.8rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px dashed #d7d7d7;
  transition: color 0.3s ease;
}

.image-text {
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  font-size: 1.05rem;
  color: #454040;
  letter-spacing: 1px;
  line-height: 1.5em;
  display: block;
  transition: color 0.3s ease;
}

.love-card:hover .image-text {
  color: #e2e2e2;
}

.love-card:hover .image-date {
  color: #bbb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .love-img-container {
    padding: 1rem;
  }
  
  .image-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .love-card {
    max-width: 100%;
  }
  
  .love-image {
    height: 20rem;
  }
}

@media (max-width: 480px) {
  .love-image {
    height: 18rem;
  }
  
  .love-img-content {
    padding: 1rem;
  }
}

/* 加载和空状态样式 */
.loading-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
  margin-top: 2rem;
}
</style>