<template>
	<div class="love-list-page">
		<div class="list-container">
			<h2 class="title">总有些惊奇的际遇 比如说我遇见你</h2>
			
			<!-- 骨架屏 -->
			<div v-if="loading" class="skeleton-container">
				<el-skeleton :rows="12" animated>
					<template #template>
						<div v-for="n in 12" :key="n" class="skeleton-item">
							<el-skeleton-item variant="circle" style="width: 28px; height: 28px; margin-right: 24px;" />
							<el-skeleton-item variant="text" style="width: 60%; height: 18px;" />
						</div>
					</template>
				</el-skeleton>
			</div>
			
			<!-- 实际内容 -->
			<ul v-else class="love-list">
				<li v-for="item in loveList" :key="item.text" class="list-item">
					<div class="checkbox">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" 
							 class="check-icon" :style="{ color: item.isCompleted ? '#4caf50' : '#e0e0e0' }">
							<path
								fill-rule="evenodd"
								d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
								clip-rule="evenodd"
							/>
						</svg>
					</div>
					<span class="text">{{ item.text }}</span>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { getLoveList } from '@/api/index.js';
import { ElMessage} from 'element-plus';

const loading = ref(true)
const loveListMock = ref([
	{ text: '一起期待未来甜蜜小生活 🥰', isCompleted: false },
	{ text: '一起为我们的小家添置东西 🏠', isCompleted: false },
	{ text: '一起挑选婚纱 👗', isCompleted: false },
	{ text: '一起挑选戒指 💍', isCompleted: false },
	{ text: '一起去见父母 👨‍👩‍👧‍👦', isCompleted: false },
	{ text: '一起入住一次五星级酒店，看夜景 🏙️', isCompleted: false },
	{ text: '一起去许愿池许个愿 🙏', isCompleted: false },
	{ text: '一起玩一次真心话大冒险 🎭', isCompleted: false },
	{ text: '一起去听一次相声 😂', isCompleted: false },
	{ text: '一起听一次演唱会 🎤', isCompleted: false },
	{ text: '一起拍照洗照片贴房间 📷', isCompleted: false },
	{ text: '一起骑行车 🚲', isCompleted: true },
	{ text: '一起去一次动物园 🐼', isCompleted: true },
	{ text: '一起敷面膜 🧖‍♀️', isCompleted: false },
	{ text: '一起去看樱花 🌸', isCompleted: false },
]);
const loveList = ref([])
onMounted(async ()=>{
	try {
		loading.value = true
		const result = await getLoveList()
		if (result.code === 200) {
			loveList.value = result.data
			console.log('获取恋爱清单成功:', result)
		}else{
			ElMessage.error('获取恋爱清单失败')
			console.error('获取恋爱清单失败:', result.message,'状态码',result.code)
			loveList.value = loveListMock.value
		}
	} catch (error) {
		console.error('获取恋爱清单异常:', error)
		loveList.value = loveListMock.value
	} finally {
		loading.value = false
	}
})
</script>

<style scoped>
.love-list-page {
	padding: 4rem 1rem;
	min-height: 100vh;
	background-color: #fdfdfd;
	background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
	background-size: 25px 25px;
	box-sizing: border-box;
}

.list-container {
	max-width: 800px;
	margin: 0 auto;
	background: #fff;
	border-radius: 1rem;
	padding: 3rem 2.5rem;
	border: 1px solid #f0f0f0;
	box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1);
}

.title {
	text-align: center;
	font-size: 1.6rem;
	font-weight: 600;
	color: #555;
	margin-bottom: 3rem;
	font-family: 'Noto Serif SC', serif;
}

.love-list {
	list-style: none;
	padding: 0;
	margin: 0;
}

.list-item {
	display: flex;
	align-items: center;
	padding: 1.2rem 0;
	font-size: 1.15rem;
	color: #444;
	border-bottom: 1px solid #f2f2f2;
	transition: background-color 0.3s;
}

.list-item:last-child {
	border-bottom: none;
}

.list-item:hover {
	background-color: #fafafa;
}

.checkbox {
	margin-right: 1.5rem;
	display: flex;
	align-items: center;
}

.check-icon {
	width: 1.8rem;
	height: 1.8rem;
	transition: color 0.4s;
}

.text {
	line-height: 1.6;
	letter-spacing: 0.1rem;
}

.skeleton-container {
	padding: 1rem 0;
}

.skeleton-item {
	display: flex;
	align-items: center;
	padding: 1.2rem 0;
	border-bottom: 1px solid #f2f2f2;
}

.skeleton-item:last-child {
	border-bottom: none;
}
</style> 