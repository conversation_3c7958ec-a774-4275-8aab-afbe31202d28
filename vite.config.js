import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path' // 新增路径处理模块

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: { 
    alias: {
      '@': path.resolve(__dirname, './src') // 配置@指向src目录
    },
    extensions: [".js", ".ts", ".jsx", ".tsx", ".json"],
  },

  build: {
    outDir: "dist",
    minify: "esbuild", // 使用 esbuild 替代 terser 进行压缩
    chunkSizeWarningLimit: 1000, // 增加警告阈值到 1MB
    rollupOptions: {
      output: {
        // 手动分包，减少单个文件大小
        manualChunks: {
          // 将 Vue 相关库分离
          vue: ['vue', 'vue-router', 'pinia'],
          // 将 Element Plus 分离
          elementPlus: ['element-plus', '@element-plus/icons-vue'],
          // 将工具库分离
          utils: ['axios', 'dayjs', 'dompurify']
        },
        // 使用内容哈希，只有内容变化时文件名才变化
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  },
})